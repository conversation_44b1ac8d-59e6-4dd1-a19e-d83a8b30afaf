[{"name": "hot-reloader", "duration": 22, "timestamp": 253683025240, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748230059285, "traceId": "aa86861ae3345d37"}, {"name": "setup-dev-bundler", "duration": 377960, "timestamp": 253682929495, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748230059190, "traceId": "aa86861ae3345d37"}, {"name": "run-instrumentation-hook", "duration": 12, "timestamp": 253683333388, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748230059594, "traceId": "aa86861ae3345d37"}, {"name": "start-dev-server", "duration": 620636, "timestamp": 253682723309, "id": 1, "tags": {"cpus": "10", "platform": "darwin", "memory.freeMem": "132415488", "memory.totalMem": "17179869184", "memory.heapSizeLimit": "8640266240", "memory.rss": "281526272", "memory.heapTotal": "98828288", "memory.heapUsed": "72418736"}, "startTime": 1748230058984, "traceId": "aa86861ae3345d37"}, {"name": "compile-path", "duration": 4610751, "timestamp": 253725674119, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748230101935, "traceId": "aa86861ae3345d37"}, {"name": "ensure-page", "duration": 4611268, "timestamp": 253725673792, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748230101934, "traceId": "aa86861ae3345d37"}]