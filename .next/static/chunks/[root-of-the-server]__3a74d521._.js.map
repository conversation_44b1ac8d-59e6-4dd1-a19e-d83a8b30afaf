{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/pacifico_d3701179.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"pacifico_d3701179-module__Ig-y1W__className\",\n  \"variable\": \"pacifico_d3701179-module__Ig-y1W__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/pacifico_d3701179.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22hero-geometric.tsx%22,%22import%22:%22Pacifico%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22],%22variable%22:%22--font-pacifico%22}],%22variableName%22:%22pacifico%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Pacifico', 'Pacifico Fallback'\",\n        fontWeight: 400,\nfontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,2JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,YAAY;QACpB,WAAW;IAEP;AACJ;AAEA,IAAI,2JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,2JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/augment/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/augment/src/components/kokonutui/hero-geometric.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Pacifico } from \"next/font/google\"\nimport Image from \"next/image\"\nimport { cn } from \"@/lib/utils\"\n\nconst pacifico = Pacifico({\n  subsets: [\"latin\"],\n  weight: [\"400\"],\n  variable: \"--font-pacifico\",\n})\n\nfunction ElegantShape({\n  className,\n  delay = 0,\n  width = 400,\n  height = 100,\n  rotate = 0,\n  gradient = \"from-white/[0.08]\",\n}: {\n  className?: string\n  delay?: number\n  width?: number\n  height?: number\n  rotate?: number\n  gradient?: string\n}) {\n  return (\n    <motion.div\n      initial={{\n        opacity: 0,\n        y: -150,\n        rotate: rotate - 15,\n      }}\n      animate={{\n        opacity: 1,\n        y: 0,\n        rotate: rotate,\n      }}\n      transition={{\n        duration: 2.4,\n        delay,\n        ease: [0.23, 0.86, 0.39, 0.96],\n        opacity: { duration: 1.2 },\n      }}\n      className={cn(\"absolute\", className)}\n    >\n      <motion.div\n        animate={{\n          y: [0, 15, 0],\n        }}\n        transition={{\n          duration: 12,\n          repeat: Number.POSITIVE_INFINITY,\n          ease: \"easeInOut\",\n        }}\n        style={{\n          width,\n          height,\n        }}\n        className=\"relative\"\n      >\n        <div\n          className={cn(\n            \"absolute inset-0 rounded-full\",\n            \"bg-gradient-to-r to-transparent\",\n            gradient,\n            \"backdrop-blur-[2px] border-2 border-white/[0.15]\",\n            \"shadow-[0_8px_32px_0_rgba(255,255,255,0.1)]\",\n            \"after:absolute after:inset-0 after:rounded-full\",\n            \"after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2),transparent_70%)]\",\n          )}\n        />\n      </motion.div>\n    </motion.div>\n  )\n}\n\nexport default function HeroGeometric({\n  badge = \"Kokonut UI\",\n  title1 = \"Elevate Your\",\n  title2 = \"Digital Vision\",\n}: {\n  badge?: string\n  title1?: string\n  title2?: string\n}) {\n  const fadeUpVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: (i: number) => ({\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 1,\n        delay: 0.5 + i * 0.2,\n        ease: [0.25, 0.4, 0.25, 1],\n      },\n    }),\n  }\n\n  return (\n    <div className=\"relative min-h-screen w-full flex items-center justify-center overflow-hidden bg-[#030303]\">\n      <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-500/[0.05] via-transparent to-rose-500/[0.05] blur-3xl\" />\n\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <ElegantShape\n          delay={0.3}\n          width={600}\n          height={140}\n          rotate={12}\n          gradient=\"from-indigo-500/[0.15]\"\n          className=\"left-[-10%] md:left-[-5%] top-[15%] md:top-[20%]\"\n        />\n\n        <ElegantShape\n          delay={0.5}\n          width={500}\n          height={120}\n          rotate={-15}\n          gradient=\"from-rose-500/[0.15]\"\n          className=\"right-[-5%] md:right-[0%] top-[70%] md:top-[75%]\"\n        />\n\n        <ElegantShape\n          delay={0.4}\n          width={300}\n          height={80}\n          rotate={-8}\n          gradient=\"from-violet-500/[0.15]\"\n          className=\"left-[5%] md:left-[10%] bottom-[5%] md:bottom-[10%]\"\n        />\n\n        <ElegantShape\n          delay={0.6}\n          width={200}\n          height={60}\n          rotate={20}\n          gradient=\"from-amber-500/[0.15]\"\n          className=\"right-[15%] md:right-[20%] top-[10%] md:top-[15%]\"\n        />\n\n        <ElegantShape\n          delay={0.7}\n          width={150}\n          height={40}\n          rotate={-25}\n          gradient=\"from-cyan-500/[0.15]\"\n          className=\"left-[20%] md:left-[25%] top-[5%] md:top-[10%]\"\n        />\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 md:px-6\">\n        <div className=\"max-w-3xl mx-auto text-center\">\n          <motion.div\n            custom={0}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            className=\"inline-flex items-center gap-2 px-3 py-1 rounded-full bg-white/[0.03] border border-white/[0.08] mb-8 md:mb-12\"\n          >\n            <Image src=\"https://kokonutui.com/logo.svg\" alt=\"Kokonut UI\" width={20} height={20} />\n            <span className=\"text-sm text-white/60 tracking-wide\">{badge}</span>\n          </motion.div>\n\n          <motion.div custom={1} variants={fadeUpVariants} initial=\"hidden\" animate=\"visible\">\n            <h1 className=\"text-4xl sm:text-6xl md:text-8xl font-bold mb-6 md:mb-8 tracking-tight\">\n              <span className=\"bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80\">{title1}</span>\n              <br />\n              <span\n                className={cn(\n                  \"bg-clip-text text-transparent bg-gradient-to-r from-indigo-300 via-white/90 to-rose-300 \",\n                  pacifico.className,\n                )}\n              >\n                {title2}\n              </span>\n            </h1>\n          </motion.div>\n\n          <motion.div custom={2} variants={fadeUpVariants} initial=\"hidden\" animate=\"visible\">\n            <p className=\"text-base sm:text-lg md:text-xl text-white/40 mb-8 leading-relaxed font-light tracking-wide max-w-xl mx-auto px-4\">\n              Crafting exceptional digital experiences through innovative design and cutting-edge technology.\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      <div className=\"absolute inset-0 bg-gradient-to-t from-[#030303] via-transparent to-[#030303]/80 pointer-events-none\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;AAEA;AACA;AALA;;;;;;AAaA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,CAAC,EACT,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,SAAS,CAAC,EACV,WAAW,mBAAmB,EAQ/B;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,SAAS;YACT,GAAG,CAAC;YACJ,QAAQ,SAAS;QACnB;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,QAAQ;QACV;QACA,YAAY;YACV,UAAU;YACV;YACA,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;YAC9B,SAAS;gBAAE,UAAU;YAAI;QAC3B;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAE1B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBACP,GAAG;oBAAC;oBAAG;oBAAI;iBAAE;YACf;YACA,YAAY;gBACV,UAAU;gBACV,QAAQ,OAAO,iBAAiB;gBAChC,MAAM;YACR;YACA,OAAO;gBACL;gBACA;YACF;YACA,WAAU;sBAEV,cAAA,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iCACA,mCACA,UACA,oDACA,+CACA,mDACA;;;;;;;;;;;;;;;;AAMZ;KAhES;AAkEM,SAAS,cAAc,EACpC,QAAQ,YAAY,EACpB,SAAS,cAAc,EACvB,SAAS,gBAAgB,EAK1B;IACC,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS,CAAC,IAAc,CAAC;gBACvB,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,UAAU;oBACV,OAAO,MAAM,IAAI;oBACjB,MAAM;wBAAC;wBAAM;wBAAK;wBAAM;qBAAE;gBAC5B;YACF,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,QAAQ;4BACR,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAEV,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAI;oCAAiC,KAAI;oCAAa,OAAO;oCAAI,QAAQ;;;;;;8CAChF,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAGzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,QAAQ;4BAAG,UAAU;4BAAgB,SAAQ;4BAAS,SAAQ;sCACxE,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAyE;;;;;;kDACzF,6LAAC;;;;;kDACD,6LAAC;wCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,+IAAA,CAAA,UAAQ,CAAC,SAAS;kDAGnB;;;;;;;;;;;;;;;;;sCAKP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,QAAQ;4BAAG,UAAU;4BAAgB,SAAQ;4BAAS,SAAQ;sCACxE,cAAA,6LAAC;gCAAE,WAAU;0CAAoH;;;;;;;;;;;;;;;;;;;;;;0BAOvI,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;MAhHwB", "debugId": null}}]}